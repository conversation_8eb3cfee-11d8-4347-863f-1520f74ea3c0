{"name": "thj-automation-control-system", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.2", "element-plus": "^2.11.1", "pinia": "^3.0.3", "vue": "^3.5.18", "vue-router": "^4.5.1", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.31.0", "@vitejs/plugin-vue": "^6.0.1", "@vitejs/plugin-vue-jsx": "^5.0.1", "@vue/eslint-config-prettier": "^10.2.0", "eslint": "^9.31.0", "eslint-plugin-vue": "~10.3.0", "globals": "^16.3.0", "prettier": "3.6.2", "vite": "^7.0.6", "vite-plugin-vue-devtools": "^8.0.0"}}