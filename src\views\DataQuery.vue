<template>
  <div class="data-query-container">
    <!-- Tab栏 -->
    <div class="tabs-container">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="操作记录" name="operation"></el-tab-pane>
        <el-tab-pane label="故障记录" name="fault"></el-tab-pane>
        <el-tab-pane label="水深记录" name="depth"></el-tab-pane>
        <el-tab-pane label="运行记录" name="running"></el-tab-pane>
      </el-tabs>
    </div>

    <!-- 筛选行 -->
    <div class="filter-container">
      <div class="filter-row">
        <div class="filter-left">
          <!-- 设备名称 -->
          <el-input
            v-model="filters.deviceName"
            placeholder="请输入设备名称"
            style="width: 200px; margin-right: 16px"
            clearable
          />

          <!-- 设备编码 -->
          <el-select
            v-model="filters.deviceCode"
            placeholder="请选择设备编码"
            style="width: 200px; margin-right: 16px"
            clearable
          >
            <el-option
              v-for="item in deviceCodeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>

          <!-- 操作员（水深记录和运行记录没有该筛选项） -->
          <el-select
            v-if="activeTab !== 'depth' && activeTab !== 'running'"
            v-model="filters.operator"
            placeholder="请选择操作员"
            style="width: 200px; margin-right: 16px"
            clearable
          >
            <el-option
              v-for="item in operatorOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>

          <!-- 选择时间 -->
          <el-date-picker
            v-model="filters.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 350px"
          />
        </div>

        <div class="filter-right">
          <el-button @click="handleReset">重置</el-button>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button type="success" @click="handleExport">导出</el-button>
        </div>
      </div>
    </div>

    <!-- 表格容器 -->
    <div class="table-container">
      <div class="table-wrapper">
        <el-table :data="currentTableData" border v-loading="loading" :height="tableHeight">
          <!-- 序号列 -->
          <el-table-column
            type="index"
            label="序号"
            width="80"
            :index="(index) => (currentPage - 1) * pageSize + index + 1"
          />

          <!-- 动态列 -->
          <el-table-column
            v-for="column in currentColumns"
            :key="column.prop"
            :prop="column.prop"
            :label="column.label"
            show-overflow-tooltip
          />
        </el-table>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import * as XLSX from 'xlsx'

// 响应式数据
const activeTab = ref('operation')
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableHeight = ref(400)

// 筛选条件
const filters = ref({
  deviceName: '',
  deviceCode: '',
  operator: '',
  dateRange: [],
})

// 下拉选项数据
const deviceCodeOptions = ref([
  { label: 'DEV001', value: 'DEV001' },
  { label: 'DEV002', value: 'DEV002' },
  { label: 'DEV003', value: 'DEV003' },
  { label: 'DEV004', value: 'DEV004' },
])

const operatorOptions = ref([
  { label: '张三', value: '张三' },
  { label: '李四', value: '李四' },
  { label: '王五', value: '王五' },
])

// Mock数据
const mockData = ref({
  operation: [
    {
      deviceCode: 'DEV001',
      deviceName: '水泵设备A',
      controlStatus: '运行中',
      action: '启动',
      operationCenter: '中央控制室',
      operator: '张三',
      operationTime: '2024-01-15 10:30:00',
    },
    {
      deviceCode: 'DEV002',
      deviceName: '阀门设备B',
      controlStatus: '停止',
      action: '关闭',
      operationCenter: '现场控制室',
      operator: '李四',
      operationTime: '2024-01-15 11:45:00',
    },
  ],
  fault: [
    {
      deviceCode: 'DEV001',
      deviceName: '水泵设备A',
      faultInfo: '电机过热',
      operationCenter: '中央控制室',
      operator: '张三',
      operationTime: '2024-01-15 09:15:00',
    },
    {
      deviceCode: 'DEV003',
      deviceName: '传感器设备C',
      faultInfo: '信号异常',
      operationCenter: '现场控制室',
      operator: '王五',
      operationTime: '2024-01-15 14:20:00',
    },
  ],
  depth: [
    {
      deviceCode: 'DEV004',
      deviceName: '水位传感器D',
      waterDepth: '2.5米',
      updateTime: '2024-01-15 12:00:00',
    },
    {
      deviceCode: 'DEV005',
      deviceName: '水位传感器E',
      waterDepth: '3.2米',
      updateTime: '2024-01-15 12:05:00',
    },
  ],
  running: [
    {
      deviceCode: 'DEV001',
      deviceName: '水泵设备A',
      startTime: '2024-01-15 08:00:00',
      endTime: '2024-01-15 16:00:00',
      duration: '8小时',
      operationTime: '2024-01-15 16:00:00',
    },
    {
      deviceCode: 'DEV002',
      deviceName: '阀门设备B',
      startTime: '2024-01-15 09:30:00',
      endTime: '2024-01-15 15:30:00',
      duration: '6小时',
      operationTime: '2024-01-15 15:30:00',
    },
  ],
})

// 表格列配置
const columnConfigs = {
  operation: [
    { prop: 'deviceCode', label: '设备编码' },
    { prop: 'deviceName', label: '设备名称' },
    { prop: 'controlStatus', label: '控制状态' },
    { prop: 'action', label: '动作' },
    { prop: 'operationCenter', label: '操作中心' },
    { prop: 'operator', label: '操作员' },
    { prop: 'operationTime', label: '操作时间' },
  ],
  fault: [
    { prop: 'deviceCode', label: '设备编码' },
    { prop: 'deviceName', label: '设备名称' },
    { prop: 'faultInfo', label: '故障信息' },
    { prop: 'operationCenter', label: '操作中心' },
    { prop: 'operator', label: '操作员' },
    { prop: 'operationTime', label: '操作时间' },
  ],
  depth: [
    { prop: 'deviceCode', label: '设备编码' },
    { prop: 'deviceName', label: '设备名称' },
    { prop: 'waterDepth', label: '水深' },
    { prop: 'updateTime', label: '更新时间' },
  ],
  running: [
    { prop: 'deviceCode', label: '设备编码' },
    { prop: 'deviceName', label: '设备名称' },
    { prop: 'startTime', label: '开始时间' },
    { prop: 'endTime', label: '关闭时间' },
    { prop: 'duration', label: '开始时长/运行时长' },
    { prop: 'operationTime', label: '操作时间' },
  ],
}

// 计算属性
const currentColumns = computed(() => {
  return columnConfigs[activeTab.value] || []
})

const currentTableData = computed(() => {
  return mockData.value[activeTab.value] || []
})

// 方法
const handleTabChange = (tabName) => {
  activeTab.value = tabName
  currentPage.value = 1
  total.value = currentTableData.value.length
}

const handleReset = () => {
  filters.value = {
    deviceName: '',
    deviceCode: '',
    operator: '',
    dateRange: [],
  }
}

const handleQuery = () => {
  loading.value = true
  // 模拟查询延迟
  setTimeout(() => {
    loading.value = false
    // 这里可以根据筛选条件过滤数据
    total.value = currentTableData.value.length
  }, 500)
}

const handleExport = () => {
  const data = currentTableData.value
  const columns = currentColumns.value

  // 创建工作表数据
  const wsData = []

  // 添加表头
  const headers = ['序号', ...columns.map((col) => col.label)]
  wsData.push(headers)

  // 添加数据行
  data.forEach((row, index) => {
    const rowData = [index + 1, ...columns.map((col) => row[col.prop] || '')]
    wsData.push(rowData)
  })

  // 创建工作簿
  const wb = XLSX.utils.book_new()
  const ws = XLSX.utils.aoa_to_sheet(wsData)

  // 设置列宽
  const colWidths = [{ wch: 8 }, ...columns.map(() => ({ wch: 20 }))]
  ws['!cols'] = colWidths

  // 添加工作表到工作簿
  const tabNames = {
    operation: '操作记录',
    fault: '故障记录',
    depth: '水深记录',
    running: '运行记录',
  }

  XLSX.utils.book_append_sheet(wb, ws, tabNames[activeTab.value])

  // 导出文件
  const fileName = `${tabNames[activeTab.value]}_${new Date().toISOString().slice(0, 10)}.xlsx`
  XLSX.writeFile(wb, fileName)
}

const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
}

const handleCurrentChange = (val) => {
  currentPage.value = val
}

// 计算表格高度
const calculateTableHeight = () => {
  nextTick(() => {
    // 计算可用高度：视窗高度 - 其他元素高度
    const windowHeight = window.innerHeight
    const tabsHeight = 60 // tabs 高度
    const filterHeight = 100 // 筛选区域高度
    const paginationHeight = 60 // 分页高度
    const padding = 40 // 容器内边距

    tableHeight.value = windowHeight - tabsHeight - filterHeight - paginationHeight - padding
  })
}

// 生命周期
onMounted(() => {
  total.value = currentTableData.value.length
  calculateTableHeight()

  // 监听窗口大小变化
  window.addEventListener('resize', calculateTableHeight)
})

// 清理事件监听器
onUnmounted(() => {
  window.removeEventListener('resize', calculateTableHeight)
})
</script>

<style scoped>
.data-query-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 20px;
  overflow: hidden;
}

.tabs-container {
  flex-shrink: 0;
  background-color: #fff;
  border-radius: 4px;
  padding: 0 20px;
}

.filter-container {
  flex-shrink: 0;
  margin-top: 20px;
}

.filter-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
}

.filter-left {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.filter-right {
  display: flex;
  gap: 12px;
}

.table-container {
  flex: 1;
  margin-top: 20px;
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.table-wrapper {
  flex: 1;
  overflow: hidden;
}

.pagination-container {
  flex-shrink: 0;
  margin-top: 20px;
  background-color: #fff;
  border-radius: 4px;
  padding: 15px 20px;
  display: flex;
  justify-content: center;
}

/* 覆盖 Element Plus 表格样式，确保表头占满宽度 */
:deep(.el-table) {
  width: 100% !important;
}

:deep(.el-table__header-wrapper) {
  width: 100% !important;
}

:deep(.el-table__body-wrapper) {
  width: 100% !important;
}

/* 确保表格列平均分布 */
:deep(.el-table .el-table__cell) {
  text-align: center;
}

:deep(.el-table th.el-table__cell) {
  background-color: #f5f7fa;
  font-weight: 600;
}
</style>
